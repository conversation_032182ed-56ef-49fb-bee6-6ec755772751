"use client";

import { useState, useRef, useEffect } from "react";
import { useUser } from "@stackframe/stack";
import { Task } from "@/lib/db";
import {
  useSubtasksQuery,
  useAddSubtaskMutation,
  useEditTaskMutation,
  useRemoveTaskMutation,
} from "@/lib/queries";
import { useUndoRedoContext } from "@/contexts/undo-redo-context";
import { UndoRedoAction } from "@/lib/types";
import { v4 as uuidv4 } from "uuid";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { GlassCard, GlassCardContent } from "@/components/ui/glass-card";
import { Checkbox } from "@/components/ui/checkbox";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Plus, Trash, Pencil, Check, X } from "lucide-react";

interface SubtaskManagerProps {
  parentTask: Task;
  listId: string;
  sortOption: string;
  onSubtaskUpdated?: (updatedSubtask?: Task, statusChanged?: boolean) => void;
  onSubtaskDeleted?: (deletedSubtaskId?: string) => void;
}

export function SubtaskManager({
  parentTask,
  listId,
  sortOption,
  onSubtaskUpdated,
  onSubtaskDeleted,
}: SubtaskManagerProps) {
  const user = useUser();
  const [newSubtaskTitle, setNewSubtaskTitle] = useState("");
  const [isAddingSubtask, setIsAddingSubtask] = useState(false);
  const [editingSubtaskId, setEditingSubtaskId] = useState<string | null>(null);
  const [editedTitle, setEditedTitle] = useState("");
  const [deleteSubtaskId, setDeleteSubtaskId] = useState<string | null>(null);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);

  // Refs
  const newSubtaskInputRef = useRef<HTMLInputElement>(null);
  const editInputRef = useRef<HTMLInputElement>(null);

  // TanStack Query hooks
  const { data: subtasks = [], isLoading } = useSubtasksQuery(parentTask.id, user?.id || "");
  const addSubtaskMutation = useAddSubtaskMutation(parentTask.id, listId, sortOption);
  const editTaskMutation = useEditTaskMutation(listId, sortOption);
  const removeTaskMutation = useRemoveTaskMutation(listId, sortOption);

  // Undo/Redo context
  const { addAction } = useUndoRedoContext();

  // Focus inputs when editing starts
  useEffect(() => {
    if (isAddingSubtask && newSubtaskInputRef.current) {
      newSubtaskInputRef.current.focus();
    }
  }, [isAddingSubtask]);

  useEffect(() => {
    if (editingSubtaskId && editInputRef.current) {
      editInputRef.current.focus();
      editInputRef.current.select();
    }
  }, [editingSubtaskId]);

  const handleAddSubtask = async () => {
    if (!user || !newSubtaskTitle.trim()) return;

    try {
      const result = await addSubtaskMutation.mutateAsync({
        userId: user.id,
        title: newSubtaskTitle.trim(),
      });

      if (result) {
        // Add undo action
        const undoAction: UndoRedoAction = {
          id: uuidv4(),
          type: 'subtask_create',
          timestamp: Date.now(),
          description: `Created subtask "${result.title}"`,
          subtask: result,
          parentTaskId: parentTask.id,
          listId: listId,
        };
        addAction(undoAction);

        setNewSubtaskTitle("");
        setIsAddingSubtask(false);
        onSubtaskUpdated?.(result, false);
      }
    } catch (error) {
      console.error('Error creating subtask:', error);
    }
  };

  const handleEditSubtask = async (subtaskId: string) => {
    if (!user || !editedTitle.trim()) {
      setEditingSubtaskId(null);
      setEditedTitle("");
      return;
    }

    const subtask = subtasks.find(s => s.id === subtaskId);
    if (!subtask || editedTitle.trim() === subtask.title) {
      setEditingSubtaskId(null);
      setEditedTitle("");
      return;
    }

    try {
      const result = await editTaskMutation.mutateAsync({
        taskId: subtaskId,
        userId: user.id,
        data: { title: editedTitle.trim() }
      });

      if (result) {
        // Add undo action
        const undoAction: UndoRedoAction = {
          id: uuidv4(),
          type: 'task_edit',
          timestamp: Date.now(),
          description: `Edited subtask title`,
          taskId: subtaskId,
          listId: listId,
          previousData: { title: subtask.title },
          newData: { title: editedTitle.trim() },
        };
        addAction(undoAction);

        setEditingSubtaskId(null);
        setEditedTitle("");
        onSubtaskUpdated?.(result, false);
      }
    } catch (error) {
      console.error('Error editing subtask:', error);
      setEditingSubtaskId(null);
      setEditedTitle("");
    }
  };

  const handleDeleteSubtask = async () => {
    if (!user || !deleteSubtaskId) return;

    const subtask = subtasks.find(s => s.id === deleteSubtaskId);
    if (!subtask) return;

    try {
      const success = await removeTaskMutation.mutateAsync({
        taskId: deleteSubtaskId,
        userId: user.id,
      });

      if (success) {
        // Add undo action
        const undoAction: UndoRedoAction = {
          id: uuidv4(),
          type: 'subtask_delete',
          timestamp: Date.now(),
          description: `Deleted subtask "${subtask.title}"`,
          subtask: subtask,
          parentTaskId: parentTask.id,
          listId: listId,
          position: subtask.position,
        };
        addAction(undoAction);

        setIsDeleteDialogOpen(false);
        setDeleteSubtaskId(null);
        onSubtaskDeleted?.(deleteSubtaskId);
      }
    } catch (error) {
      console.error('Error deleting subtask:', error);
    }
  };

  const handleSubtaskStatusChange = async (subtaskId: string, newStatus: string) => {
    if (!user) return;

    const subtask = subtasks.find(s => s.id === subtaskId);
    if (!subtask) return;

    try {
      const result = await editTaskMutation.mutateAsync({
        taskId: subtaskId,
        userId: user.id,
        data: { status: newStatus }
      });

      if (result) {
        // Add undo action
        const undoAction: UndoRedoAction = {
          id: uuidv4(),
          type: 'task_status_change',
          timestamp: Date.now(),
          description: `${newStatus === 'completed' ? 'Completed' : 'Uncompleted'} subtask "${subtask.title}"`,
          taskId: subtaskId,
          listId: listId,
          previousStatus: subtask.status,
          newStatus: newStatus,
        };
        addAction(undoAction);

        onSubtaskUpdated?.(result, true);
      }
    } catch (error) {
      console.error('Error updating subtask status:', error);
    }
  };

  const startEditingSubtask = (subtask: Task) => {
    setEditingSubtaskId(subtask.id);
    setEditedTitle(subtask.title);
  };

  const cancelEditing = () => {
    setEditingSubtaskId(null);
    setEditedTitle("");
  };

  const startDeleteSubtask = (subtaskId: string) => {
    setDeleteSubtaskId(subtaskId);
    setIsDeleteDialogOpen(true);
  };

  if (isLoading) {
    return (
      <div className="grid gap-2">
        <div className="text-sm font-medium text-muted-foreground">Subtasks</div>
        <div className="text-sm text-muted-foreground">Loading subtasks...</div>
      </div>
    );
  }

  return (
    <>
      <div className="grid gap-2">
        <div className="flex items-center justify-between">
          <div className="text-sm font-medium text-muted-foreground">
            Subtasks {subtasks.length > 0 && (
              <Badge variant="secondary" className="ml-2 text-xs">
                {subtasks.filter(s => s.status === 'completed').length}/{subtasks.length}
              </Badge>
            )}
          </div>
          {!isAddingSubtask && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsAddingSubtask(true)}
              className="h-6 px-2 text-xs"
            >
              <Plus className="h-3 w-3 mr-1" />
              Add
            </Button>
          )}
        </div>

        {/* Existing Subtasks */}
        {subtasks.length > 0 && (
          <div className="space-y-1">
            {subtasks.map((subtask) => (
              <GlassCard key={subtask.id} className="border border-border/30" intensity="subtle">
                <GlassCardContent className="py-2 px-3">
                  <div className="flex items-center gap-2">
                    <Checkbox
                      checked={subtask.status === 'completed'}
                      onCheckedChange={(checked) => 
                        handleSubtaskStatusChange(subtask.id, checked ? 'completed' : 'todo')
                      }
                      className="h-4 w-4"
                    />
                    
                    <div className="flex-1 min-w-0">
                      {editingSubtaskId === subtask.id ? (
                        <div className="flex items-center gap-1">
                          <Input
                            ref={editInputRef}
                            value={editedTitle}
                            onChange={(e) => setEditedTitle(e.target.value)}
                            onKeyDown={(e) => {
                              if (e.key === 'Enter') {
                                handleEditSubtask(subtask.id);
                              } else if (e.key === 'Escape') {
                                cancelEditing();
                              }
                            }}
                            className="h-6 text-xs"
                          />
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={() => handleEditSubtask(subtask.id)}
                            className="h-6 w-6 p-0"
                          >
                            <Check className="h-3 w-3" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={cancelEditing}
                            className="h-6 w-6 p-0"
                          >
                            <X className="h-3 w-3" />
                          </Button>
                        </div>
                      ) : (
                        <div
                          className={`text-xs cursor-pointer ${
                            subtask.status === 'completed' 
                              ? 'line-through text-muted-foreground' 
                              : ''
                          }`}
                          onClick={() => startEditingSubtask(subtask)}
                        >
                          {subtask.title}
                        </div>
                      )}
                    </div>

                    {editingSubtaskId !== subtask.id && (
                      <div className="flex items-center gap-1">
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={() => startEditingSubtask(subtask)}
                          className="h-6 w-6 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
                        >
                          <Pencil className="h-3 w-3" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={() => startDeleteSubtask(subtask.id)}
                          className="h-6 w-6 p-0 opacity-0 group-hover:opacity-100 transition-opacity text-destructive hover:text-destructive"
                        >
                          <Trash className="h-3 w-3" />
                        </Button>
                      </div>
                    )}
                  </div>
                </GlassCardContent>
              </GlassCard>
            ))}
          </div>
        )}

        {/* Add New Subtask */}
        {isAddingSubtask && (
          <div className="flex items-center gap-2">
            <Input
              ref={newSubtaskInputRef}
              value={newSubtaskTitle}
              onChange={(e) => setNewSubtaskTitle(e.target.value)}
              onKeyDown={(e) => {
                if (e.key === 'Enter') {
                  handleAddSubtask();
                } else if (e.key === 'Escape') {
                  setNewSubtaskTitle("");
                  setIsAddingSubtask(false);
                }
              }}
              placeholder="Enter subtask title..."
              className="h-8 text-sm"
            />
            <Button
              variant="ghost"
              size="icon"
              onClick={handleAddSubtask}
              disabled={!newSubtaskTitle.trim()}
              className="h-8 w-8 p-0"
            >
              <Check className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="icon"
              onClick={() => {
                setNewSubtaskTitle("");
                setIsAddingSubtask(false);
              }}
              className="h-8 w-8 p-0"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        )}
      </div>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogTitle className="sr-only">
            Delete Subtask
          </AlertDialogTitle>
          <AlertDialogDescription>
            Are you sure you want to delete this subtask?
            <br />
            <br />
            This action cannot be undone.
          </AlertDialogDescription>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDeleteSubtask}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              Delete Subtask
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}
