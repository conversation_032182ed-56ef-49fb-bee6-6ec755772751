"use client";

import { useState, useRef, useEffect, useMemo, useCallback } from "react";
import { useUser } from "@stackframe/stack";
import { formatDateTime, hasTimeComponent } from "@/lib/date-utils";
import { Task, Tag } from "@/lib/db";
import { getContrastTextColor } from "@/lib/list-colors";
import { searchTags } from "@/app/actions/tags";
import {
  useTagsQuery,
  useOptimizedTaskTags,
  useEditTagMutation,
  useDeleteTagMutation,
  useAddTaskTagMutation,
  useRemoveTaskTagMutation,
  useCreateTagMutation,
  useEditTaskMutation,
  useRemoveTaskMutation,
  useDuplicateTaskMutation,
  useMoveTaskToListMutation,
  useReorderTasksMutation,
  useListsQuery,
} from "@/lib/queries";
import { useUndoRedoContext } from "@/contexts/undo-redo-context";
import { UndoRedoAction } from "@/lib/types";
import { v4 as uuidv4 } from "uuid";
import { GlassCard, GlassCardContent } from "@/components/ui/glass-card";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { Badge } from "@/components/ui/badge";

import { Calendar, Copy, Pencil, ArrowLeftRight, Trash, Check, ChevronDown, ChevronRight } from "lucide-react";
import { EditTaskDialog } from "./edit-task-dialog";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";


import { DateTimePicker } from "@/components/ui/datetime-picker";
import { TagPill } from "@/components/ui/tag-pill";
import { InlineTagPicker } from "@/components/ui/inline-tag-picker";

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";
import { useSortable } from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  DragEndEvent,
  DragStartEvent,
  DragOverlay,
} from "@dnd-kit/core";
import {
  SortableContext,
  sortableKeyboardCoordinates,
  verticalListSortingStrategy,
} from "@dnd-kit/sortable";
import { restrictToVerticalAxis } from "@dnd-kit/modifiers";
import { useMediaQuery } from "@/hooks/use-media-query";

interface TaskItemProps {
  task: Task;
  onUpdated: (updatedTask?: Task, statusChanged?: boolean) => void;
  onDeleted: (deletedTaskId?: string) => void;
  isDraggable: boolean;
  isAnyTaskDragging: boolean;
  listId: string;
  sortOption: string;
  listColor?: string | null;
  taskCounts?: Record<string, number>;
  forceSelected?: boolean; // Force the task to appear selected (for drag overlay)
  taskMode?: "completion" | "selection";
  selectedTaskIds?: Set<string>;
  onTaskSelectionChange?: (selectedIds: Set<string>) => void;
  lastSelectedTaskId?: string | null;
  isInlineEditEnabled?: boolean; // Controls whether tag editing is enabled
  activeActionIconsTaskId?: string | null; // Which task should show action icons in completion mode
  onActionIconsChange?: (taskId: string | null) => void; // Callback to change which task shows action icons
  // Task navigation props
  allTasks?: Task[]; // All tasks in the current list for navigation
  onNavigateToTask?: (task: Task) => void; // Callback to switch to a different task
  isTagFiltered?: boolean; // Whether we're in tag-filtered view
  // Subtask props
  isSubtask?: boolean; // Whether this task is being rendered as a subtask
  parentTask?: Task; // Reference to parent task if this is a subtask
  isInModal?: boolean; // Whether this task is being rendered in a modal context
}

export function TaskItem({
  task,
  onUpdated,
  onDeleted,
  isDraggable,
  isAnyTaskDragging,
  listId,
  sortOption,
  listColor,
  taskCounts,
  forceSelected = false,
  taskMode = "completion",
  selectedTaskIds = new Set(),
  onTaskSelectionChange,
  lastSelectedTaskId = null,
  isInlineEditEnabled = true,
  activeActionIconsTaskId = null,
  onActionIconsChange,
  allTasks = [],
  onNavigateToTask,
  isTagFiltered = false,
  isSubtask = false,
  parentTask,
  isInModal = false,
}: TaskItemProps) {
  const user = useUser();
  const isMobile = useMediaQuery("(max-width: 767px)");
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);

  // Task navigation states for edit dialog
  const [currentEditingTask, setCurrentEditingTask] = useState<Task>(task);
  const [currentEditingIndex, setCurrentEditingIndex] = useState(0);

  // Memoize allTasks length to prevent infinite re-renders
  const allTasksLength = useMemo(() => allTasks?.length || 0, [allTasks?.length]);

  // Initialize editing task and index when dialog opens
  useEffect(() => {
    if (isEditDialogOpen && allTasksLength > 0) {
      const taskIndex = allTasks.findIndex(t => t.id === task.id);
      setCurrentEditingTask(task);
      setCurrentEditingIndex(taskIndex >= 0 ? taskIndex : 0);
    }
  }, [isEditDialogOpen, task.id, task.title, task.description, task.due_date, task.status, allTasksLength]);

  // Update currentEditingTask index when allTasks changes (for reordering)
  // But do NOT update the task data itself to avoid jumping back to original task
  useEffect(() => {
    if (isEditDialogOpen && allTasksLength > 0 && currentEditingTask) {
      // Only update the index if the task still exists and the index has changed
      const newIndex = allTasks.findIndex(t => t.id === currentEditingTask.id);
      if (newIndex >= 0 && newIndex !== currentEditingIndex) {
        setCurrentEditingIndex(newIndex);
      }
    }
  }, [allTasksLength, isEditDialogOpen, currentEditingTask?.id, currentEditingIndex]);

  // Handle task navigation in edit dialog
  const handleNavigateToTask = (newTask: Task, newIndex: number) => {
    // Update local state
    setCurrentEditingTask(newTask);
    setCurrentEditingIndex(newIndex);

    // Notify parent component if callback is provided
    if (onNavigateToTask) {
      onNavigateToTask(newTask);
    }
  };

  // Handle task updates from the edit dialog
  const handleTaskUpdatedFromDialog = (updatedTask?: Task, statusChanged?: boolean) => {
    // If the updated task matches the current editing task, update our local state
    if (updatedTask && currentEditingTask.id === updatedTask.id) {
      setCurrentEditingTask(updatedTask);
    }

    // Call the original onUpdated callback
    onUpdated(updatedTask, statusChanged);
  };
  const [isCompleted, setIsCompleted] = useState(task.status === "completed");
  const [isDeleteTagDialogOpen, setIsDeleteTagDialogOpen] = useState(false);
  const [tagToDelete, setTagToDelete] = useState<Tag | null>(null);
  const [isSelected, setIsSelected] = useState(false); // Selection state for both mobile and desktop

  // Check if task is selected in global selection state
  const isGloballySelected = selectedTaskIds.has(task.id);

  // Check if this task should show action icons in completion mode
  const shouldShowActionIcons = activeActionIconsTaskId === task.id;



  // Undo/Redo functionality
  const { addAction } = useUndoRedoContext();

  // TanStack Query hooks with optimized tag loading
  // Use individual queries when in tag-filtered view since tasks come from different lists
  const { data: taskTags = [] } = useOptimizedTaskTags(task.id, task.list_id, user?.id || "", isTagFiltered);
  const { data: availableTags = [] } = useTagsQuery(user?.id || "");
  const { data: availableLists = [] } = useListsQuery(user?.id || "");

  // Find subtasks from allTasks array instead of making a separate query
  // Memoize subtasks to prevent infinite re-renders
  const subtasks = useMemo(() => {
    if (task.parent_task_id) return [];

    const filteredSubtasks = (allTasks || [])
      .filter(t => t.parent_task_id === task.id)
      .sort((a, b) => a.position - b.position);

    return filteredSubtasks;
  }, [task.parent_task_id, task.id, allTasksLength]);

  // Separate completed and incomplete subtasks
  const incompleteSubtasks = useMemo(() => {
    return subtasks.filter(subtask => subtask.status !== 'completed');
  }, [subtasks]);

  const completedSubtasks = useMemo(() => {
    return subtasks.filter(subtask => subtask.status === 'completed');
  }, [subtasks]);

  // Subtask drag and drop state
  const [subtaskItems, setSubtaskItems] = useState<Task[]>([]);
  const [isSubtaskDragging, setIsSubtaskDragging] = useState(false);
  const [activeSubtask, setActiveSubtask] = useState<Task | null>(null);

  // Completed subtasks state
  const [isCompletedSubtasksOpen, setIsCompletedSubtasksOpen] = useState(false);

  // Update subtask items when subtasks change - use only incomplete subtasks for drag and drop
  useEffect(() => {
    if (task.parent_task_id) {
      setSubtaskItems([]);
      return;
    }

    // Only include incomplete subtasks in the draggable list
    setSubtaskItems(incompleteSubtasks);
  }, [task.parent_task_id, task.id, incompleteSubtasks]);



  // Task mutations - use task's actual list_id for consistency
  const editTaskMutation = useEditTaskMutation(task.list_id, sortOption as any);
  const removeTaskMutation = useRemoveTaskMutation(task.list_id, sortOption as any);
  const duplicateTaskMutation = useDuplicateTaskMutation(task.list_id, sortOption as any);
  const moveTaskMutation = useMoveTaskToListMutation(task.list_id, sortOption as any);
  const reorderTasksMutation = useReorderTasksMutation(task.list_id, sortOption as any);

  // Tag mutations
  const editTagMutation = useEditTagMutation(user?.id || "");
  const deleteTagMutation = useDeleteTagMutation(user?.id || "");
  const addTaskTagMutation = useAddTaskTagMutation(task.id, user?.id || "", task.list_id);
  const removeTaskTagMutation = useRemoveTaskTagMutation(task.id, user?.id || "", task.list_id);
  const createTagMutation = useCreateTagMutation(user?.id || "");

  // Inline editing states
  const [isEditingTitle, setIsEditingTitle] = useState(false);
  const [isEditingDescription, setIsEditingDescription] = useState(false);
  const [isEditingDueDate, setIsEditingDueDate] = useState(false);
  const [editedTitle, setEditedTitle] = useState(task.title);
  const [editedDescription, setEditedDescription] = useState(task.description || "");
  const [editedDueDate, setEditedDueDate] = useState<Date | undefined | null>(
    task.due_date ? new Date(task.due_date) : undefined
  );
  const [isSaving, setIsSaving] = useState(false);

  // Refs for input elements
  const titleInputRef = useRef<HTMLInputElement>(null);
  const descriptionInputRef = useRef<HTMLTextAreaElement>(null);
  const datePickerRef = useRef<HTMLDivElement>(null);

  // Update local state when task changes
  useEffect(() => {
    setEditedTitle(task.title);
    setEditedDescription(task.description || "");
    setEditedDueDate(task.due_date ? new Date(task.due_date) : undefined);
    setIsCompleted(task.status === "completed");
  }, [task]);

  // Simplified drag state tracking
  const cardRef = useRef<HTMLDivElement>(null);



  // Focus input when editing starts
  useEffect(() => {
    if (isEditingTitle && titleInputRef.current) {
      titleInputRef.current.focus();
    }
  }, [isEditingTitle]);

  useEffect(() => {
    if (isEditingDescription && descriptionInputRef.current) {
      descriptionInputRef.current.focus();
    }
  }, [isEditingDescription]);

  // Close date editor when other editors are opened
  useEffect(() => {
    if (isEditingTitle || isEditingDescription) {
      if (isEditingDueDate) {
        setEditedDueDate(task.due_date ? new Date(task.due_date) : undefined);
        setIsEditingDueDate(false);
      }
    }
  }, [isEditingTitle, isEditingDescription, isEditingDueDate, task.due_date]);

  // Cancel all editing states when inline editing is disabled
  useEffect(() => {
    if (!isInlineEditEnabled) {
      if (isEditingTitle) {
        setEditedTitle(task.title);
        setIsEditingTitle(false);
      }
      if (isEditingDescription) {
        setEditedDescription(task.description || "");
        setIsEditingDescription(false);
      }
      if (isEditingDueDate) {
        setEditedDueDate(task.due_date ? new Date(task.due_date) : undefined);
        setIsEditingDueDate(false);
      }
    }
  }, [isInlineEditEnabled, task.title, task.description, task.due_date]);

  // Handle clicks outside date picker to dismiss it
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (!isEditingDueDate || !datePickerRef.current) return;

      const target = event.target as Node;

      // Check if click is within the date picker container
      if (datePickerRef.current.contains(target)) {
        return;
      }

      // Check if click is within a popover (DatePicker's calendar popover)
      // Popover content is rendered via portal, so we need to check for popover-related elements
      const clickedElement = target as Element;
      if (clickedElement.closest && (
        clickedElement.closest('[role="dialog"]') || // Popover content
        clickedElement.closest('[data-radix-popper-content-wrapper]') || // Radix popover wrapper
        clickedElement.closest('[data-radix-portal]') || // Radix portal
        clickedElement.closest('.rdp') || // React Day Picker calendar
        clickedElement.closest('[data-state="open"]') // Open popover state
      )) {
        return;
      }

      // Additional check for popover content by looking for common popover classes
      if (clickedElement.closest && clickedElement.closest('[data-radix-popper-content]')) {
        return;
      }

      // If we get here, it's a true outside click
      handleDismissDateEditor();
    };

    if (isEditingDueDate) {
      document.addEventListener('mousedown', handleClickOutside);
      return () => {
        document.removeEventListener('mousedown', handleClickOutside);
      };
    }
  }, [isEditingDueDate]);

  const { attributes, listeners, setNodeRef, transform, isDragging: dndIsDragging } = useSortable({
    id: task.id,
    disabled: !isDraggable || isSubtaskDragging, // Disable parent drag when subtask is dragging
    animateLayoutChanges: () => false, // Disable all layout animations for instant positioning
  });

  // Subtask drag and drop sensors - use different activation constraints to avoid conflicts
  const subtaskSensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 5, // Shorter distance for subtasks to differentiate from parent
      },
    }),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  // Handle subtask drag start
  const handleSubtaskDragStart = (event: DragStartEvent) => {
    setIsSubtaskDragging(true);
    const draggedSubtask = subtaskItems.find(subtask => subtask.id === event.active.id);
    setActiveSubtask(draggedSubtask || null);

    // Add haptic feedback for subtask drag
    if ('vibrate' in navigator) {
      navigator.vibrate(30);
    }
  };

  // Handle subtask drag end
  const handleSubtaskDragEnd = async (event: DragEndEvent) => {
    const { active, over } = event;

    setIsSubtaskDragging(false);
    setActiveSubtask(null);

    if (!over || active.id === over.id) {
      return;
    }

    const oldIndex = subtaskItems.findIndex(item => item.id === active.id);
    const newIndex = subtaskItems.findIndex(item => item.id === over.id);

    if (oldIndex === -1 || newIndex === -1) {
      return;
    }

    // Optimistically update the UI
    const newSubtaskItems = [...subtaskItems];
    const [movedItem] = newSubtaskItems.splice(oldIndex, 1);
    newSubtaskItems.splice(newIndex, 0, movedItem);
    setSubtaskItems(newSubtaskItems);

    // Update positions and reorder
    const reorderedSubtasks = newSubtaskItems.map((subtask, index) => ({
      ...subtask,
      position: index + 1,
    }));

    try {
      if (user?.id) {
        await reorderTasksMutation.mutateAsync({
          userId: user.id,
          taskIds: reorderedSubtasks.map(t => t.id),
        });
      }
    } catch (error) {
      console.error('Error reordering subtasks:', error);
      // Revert on error
      setSubtaskItems(subtasks);
    }
  };



  // Simplified conditional listeners - prioritize drag functionality and prevent conflicts
  const conditionalListeners = useMemo(() => {
    if (!isDraggable || isSubtaskDragging || isAnyTaskDragging) {
      return {};
    }
    return listeners;
  }, [isDraggable, isSubtaskDragging, isAnyTaskDragging, listeners]);

  // Maintain selection state during drag - don't clear it
  // This ensures the dragged item maintains its visual selection state

  // Simplified mobile touch handling system
  const [touchStartData, setTouchStartData] = useState<{
    time: number;
    x: number;
    y: number;
  } | null>(null);

  // Alternative pointer-based approach for mobile tap detection
  const [pointerStartData, setPointerStartData] = useState<{
    time: number;
    x: number;
    y: number;
    pointerId: number;
  } | null>(null);

  // Check if target is an interactive element that should not trigger card actions
  const isInteractiveElement = (target: Element): boolean => {
    return !!(
      target.closest('button') ||
      target.closest('input') ||
      target.closest('textarea') ||
      target.closest('[data-radix-popper-content]') ||
      target.closest('[role="dialog"]') ||
      target.closest('[data-testid="checkbox"]') ||
      target.closest('[data-testid="radio-selector"]') ||
      target.closest('[data-subtask="true"]') // Prevent parent interactions when touching subtasks
    );
  };

  // Handle mobile pointer start - alternative to touch events
  const handleMobilePointerDown = (e: React.PointerEvent) => {
    console.log('handleMobilePointerDown called for task:', task.id, {
      isMobile,
      dndIsDragging,
      isAnyTaskDragging,
      isDraggable,
      pointerType: e.pointerType
    });

    // Only handle touch pointers on mobile
    if (!isMobile || e.pointerType !== 'touch' || dndIsDragging || isAnyTaskDragging) return;

    const target = e.target as Element;

    // Don't track touches on interactive elements
    if (isInteractiveElement(target)) {
      console.log('Pointer on interactive element, ignoring');
      return;
    }

    console.log('Recording pointer start data');
    // Record pointer start data
    setPointerStartData({
      time: Date.now(),
      x: e.clientX,
      y: e.clientY,
      pointerId: e.pointerId,
    });
  };

  // Handle mobile touch start - simple tracking without complex logic
  const handleMobileTouchStart = (e: React.TouchEvent) => {
    console.log('handleMobileTouchStart called for task:', task.id, {
      isMobile,
      dndIsDragging,
      isAnyTaskDragging,
      isDraggable
    });

    if (!isMobile || dndIsDragging || isAnyTaskDragging) return;

    const touch = e.touches[0];
    const target = e.target as Element;

    // Don't track touches on interactive elements
    if (isInteractiveElement(target)) {
      console.log('Touch on interactive element, ignoring');
      return;
    }

    console.log('Recording touch start data');
    // Record touch start data
    setTouchStartData({
      time: Date.now(),
      x: touch.clientX,
      y: touch.clientY,
    });
  };

  // Handle mobile pointer up - alternative to touch events
  const handleMobilePointerUp = (e: React.PointerEvent) => {
    console.log('handleMobilePointerUp called for task:', task.id, {
      isMobile,
      dndIsDragging,
      isAnyTaskDragging,
      pointerStartData: !!pointerStartData,
      pointerType: e.pointerType
    });

    // Only handle touch pointers on mobile
    if (!isMobile || e.pointerType !== 'touch' || dndIsDragging || isAnyTaskDragging || !pointerStartData) {
      setPointerStartData(null);
      return;
    }

    const target = e.target as Element;
    const touchDuration = Date.now() - pointerStartData.time;
    const deltaX = Math.abs(e.clientX - pointerStartData.x);
    const deltaY = Math.abs(e.clientY - pointerStartData.y);
    const totalMovement = Math.sqrt(deltaX * deltaX + deltaY * deltaY);

    // Clean up pointer data
    setPointerStartData(null);

    // Don't process touches on interactive elements
    if (isInteractiveElement(target)) {
      console.log('Pointer up on interactive element, ignoring');
      return;
    }

    // Only process quick taps with minimal movement
    // This allows drag system to handle press-and-hold gestures (250ms+)
    // Increased threshold to 240ms to avoid conflict with drag system
    if (touchDuration < 240 && totalMovement < 10) {
      console.log('Processing mobile pointer tap - duration:', touchDuration, 'movement:', totalMovement);
      // Delay processing slightly to avoid conflicts with drag system
      setTimeout(() => {
        processMobileTap();
      }, 10);
    } else {
      console.log('Pointer not processed - duration:', touchDuration, 'movement:', totalMovement);
    }
  };

  // Handle mobile touch end - process based on duration and movement
  const handleMobileTouchEnd = (e: React.TouchEvent) => {
    console.log('handleMobileTouchEnd called for task:', task.id, {
      isMobile,
      dndIsDragging,
      isAnyTaskDragging,
      touchStartData: !!touchStartData
    });

    if (!isMobile || dndIsDragging || isAnyTaskDragging || !touchStartData) {
      setTouchStartData(null);
      return;
    }

    const touch = e.changedTouches[0];
    const target = e.target as Element;
    const touchDuration = Date.now() - touchStartData.time;
    const deltaX = Math.abs(touch.clientX - touchStartData.x);
    const deltaY = Math.abs(touch.clientY - touchStartData.y);
    const totalMovement = Math.sqrt(deltaX * deltaX + deltaY * deltaY);

    // Clean up touch data
    setTouchStartData(null);

    // Don't process touches on interactive elements
    if (isInteractiveElement(target)) {
      return;
    }

    // Only process quick taps with minimal movement
    // This allows drag system to handle press-and-hold gestures (250ms+)
    // Increased threshold to 240ms to avoid conflict with drag system
    if (touchDuration < 240 && totalMovement < 10) {
      console.log('Processing mobile tap - duration:', touchDuration, 'movement:', totalMovement);
      // Delay processing slightly to avoid conflicts with drag system
      setTimeout(() => {
        processMobileTap();
      }, 10);
    } else {
      console.log('Touch not processed - duration:', touchDuration, 'movement:', totalMovement);
    }
  };

  // Process mobile tap based on mode and inline editing state
  const processMobileTap = () => {
    console.log('processMobileTap called for task:', task.id, {
      taskMode,
      isInlineEditEnabled,
      shouldShowActionIcons,
      onActionIconsChange: !!onActionIconsChange
    });

    if (taskMode === "selection") {
      // Selection mode: always handle multi-selection regardless of inline editing
      if (onTaskSelectionChange) {
        const newSelectedIds = new Set(selectedTaskIds);
        if (isGloballySelected) {
          newSelectedIds.delete(task.id);
        } else {
          newSelectedIds.add(task.id);
        }
        onTaskSelectionChange(newSelectedIds);
      }
    } else if (taskMode === "completion") {
      // Completion mode: behavior depends on inline editing state
      if (isInlineEditEnabled && onActionIconsChange) {
        // Edit mode: toggle action icons visibility for this task
        const newTaskId = shouldShowActionIcons ? null : task.id;
        console.log('Setting action icons for task:', newTaskId);
        onActionIconsChange(newTaskId);
      } else {
        console.log('Not calling onActionIconsChange:', { isInlineEditEnabled, onActionIconsChange: !!onActionIconsChange });
        // Read-only mode: open edit modal when inline editing is disabled
        setIsEditDialogOpen(true);
      }
    }
  };

  // Handle desktop card click - different behavior per mode
  const handleCardClick = (e: React.MouseEvent) => {
    if (isMobile || dndIsDragging || isAnyTaskDragging) return;

    // Only handle if click is on the card itself, not on interactive elements
    const target = e.target as Element;
    if (target.closest('button') || target.closest('input') || target.closest('textarea') ||
        target.closest('[data-radix-popper-content]') || target.closest('[role="dialog"]') ||
        target.closest('[data-testid="checkbox"]') || target.closest('[data-testid="radio-selector"]')) {
      return;
    }

    // Prevent parent task interactions when clicking on subtasks
    if (target.closest('[data-subtask="true"]')) {
      return;
    }

    if (taskMode === "selection") {
      // In selection mode, use global selection state for multi-selection
      if (onTaskSelectionChange) {
        const newSelectedIds = new Set(selectedTaskIds);
        if (isGloballySelected) {
          newSelectedIds.delete(task.id);
        } else {
          newSelectedIds.add(task.id);
        }
        onTaskSelectionChange(newSelectedIds);
      }
    } else {
      // In completion mode, behavior depends on inline editing mode
      if (isInlineEditEnabled && onActionIconsChange) {
        // When inline editing is enabled, clicking shows action icons (no visual highlight)
        const newTaskId = shouldShowActionIcons ? null : task.id;
        onActionIconsChange(newTaskId);
      } else {
        // When inline editing is disabled, clicking opens edit modal (read-only mode)
        setIsEditDialogOpen(true);
      }
    }
  };

  // Handle clicks outside to deselect - only needed in completion mode for local selection and action icons
  useEffect(() => {
    if (taskMode !== "completion" || (!isSelected && !shouldShowActionIcons)) return;

    const handleClickOutside = (event: MouseEvent | TouchEvent) => {
      const target = event.target as Element;
      if (cardRef.current && !cardRef.current.contains(target)) {
        setIsSelected(false);
        if (shouldShowActionIcons && onActionIconsChange) {
          onActionIconsChange(null);
        }
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    document.addEventListener('touchstart', handleClickOutside);

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
      document.removeEventListener('touchstart', handleClickOutside);
    };
  }, [isSelected, shouldShowActionIcons, taskMode, onActionIconsChange]);

  // Optimized styles for 60fps performance and instant positioning
  const style = {
    transform: CSS.Transform.toString(transform),
    transition: 'none', // No transitions for instant positioning
    willChange: dndIsDragging ? 'transform' : 'auto', // Optimize for transform changes during drag
    // Don't set opacity inline - let CSS handle it for drag effects
    position: 'relative' as const, // Ensure proper positioning context
  };

  // Dynamic border styling
  const getBorderClass = () => {
    return 'border transition-all duration-200';
  };

  // Dynamic inline styles for selection and drag states
  const getCardStyles = (): React.CSSProperties => {
    const baseStyles: React.CSSProperties = {
      ...style,
      // Maintain card dimensions during drag
      minHeight: 'auto',
      width: '100%',
      // Add subtask styling - only apply in main list view, not in modal
      ...(isSubtask && !isInModal && {
        width: '100%', // Full width - no margin needed since they're in their own container
        marginTop: '0.25rem', // Small top margin for separation from previous subtask
        transform: style.transform ? `${style.transform}` : undefined, // Remove scaling
      }),
    };

    // Only show selection highlights in selection mode or during drag
    const shouldHighlight = taskMode === "selection"
      ? (isGloballySelected || dndIsDragging || forceSelected)
      : (dndIsDragging || forceSelected); // In completion mode, only highlight during drag

    // Subtle selection highlight for drag and selection states
    if (shouldHighlight) {
      if (listColor) {
        return {
          ...baseStyles,
          borderColor: listColor,
          boxShadow: `0 0 0 1px ${listColor}26, 0 0 4px ${listColor}14`
        };
      } else {
        return {
          ...baseStyles,
          borderColor: 'hsl(var(--primary))',
          boxShadow: '0 0 0 1px hsl(var(--primary) / 0.15), 0 0 4px hsl(var(--primary) / 0.08)'
        };
      }
    }

    return baseStyles;
  };

  const handleStatusChange = async () => {
    const newStatus = isCompleted ? "todo" : "completed";
    const previousStatus = isCompleted;

    // Optimistic update
    setIsCompleted(!isCompleted);

    if (user) {
      try {
        const updatedTask = await editTaskMutation.mutateAsync({
          taskId: task.id,
          userId: user.id,
          data: { status: newStatus }
        });
        if (updatedTask) {
          // Pass updated task data and indicate status changed
          onUpdated(updatedTask, true);
        } else {
          // Revert optimistic update on failure
          setIsCompleted(previousStatus);
        }
      } catch (error) {
        console.error("Error updating task status:", error);
        // Revert optimistic update on error
        setIsCompleted(previousStatus);
      }
    }
  };

  // Handle checkbox click based on current mode
  const handleCheckboxClick = () => {
    if (taskMode === "completion") {
      // In completion mode, checkbox completes/uncompletes the task
      handleStatusChange();
    } else {
      // In selection mode, checkbox selects/deselects the task
      if (onTaskSelectionChange) {
        const newSelectedIds = new Set(selectedTaskIds);
        if (isGloballySelected) {
          newSelectedIds.delete(task.id);
        } else {
          newSelectedIds.add(task.id);
        }
        onTaskSelectionChange(newSelectedIds);
      }
    }
  };

  const handleDelete = async () => {
    if (user) {
      try {
        const success = await removeTaskMutation.mutateAsync({
          taskId: task.id,
          userId: user.id
        });
        if (success) {
          // Pass task ID for optimistic update
          onDeleted(task.id);
        }
      } catch (error) {
        console.error("Error deleting task:", error);
      }
    }
    setIsDeleteDialogOpen(false);
  };

  const handleDuplicate = async () => {
    if (user) {
      try {
        const duplicatedTask = await duplicateTaskMutation.mutateAsync({
          taskId: task.id,
          userId: user.id
        });
        if (duplicatedTask) {
          // Record action for undo/redo
          const action: UndoRedoAction = {
            id: uuidv4(),
            type: 'task_duplicate',
            timestamp: Date.now(),
            description: `Duplicated task "${task.title}"`,
            originalTaskId: task.id,
            duplicatedTask,
            listId,
            position: 0, // Will be updated by the mutation's optimistic update logic
          };
          addAction(action);

          // Pass duplicated task for optimistic update
          onUpdated(duplicatedTask, false);
        }
      } catch (error) {
        console.error("Error duplicating task:", error);
      }
    }
  };

  const handleMoveToList = async (newListId: string) => {
    if (!user?.id || newListId === listId) return;

    try {
      // Record action for undo/redo before moving
      const action: UndoRedoAction = {
        id: uuidv4(),
        type: 'task_move_list',
        timestamp: Date.now(),
        description: `Moved task "${task.title}" to another list`,
        taskId: task.id,
        task,
        sourceListId: listId,
        targetListId: newListId,
        sourcePosition: 0, // Will be determined by current position
        targetPosition: 0, // Will be determined by target list
      };
      addAction(action);

      const movedTask = await moveTaskMutation.mutateAsync({
        taskId: task.id,
        userId: user.id,
        newListId,
      });

      if (movedTask) {
        // Remove task from current list
        onDeleted(task.id);
      }
    } catch (error) {
      console.error("Error moving task:", error);
    }
  };

  // Save title changes
  const handleSaveTitle = async () => {
    if (!user || editedTitle === task.title || !editedTitle.trim()) {
      setIsEditingTitle(false);
      setEditedTitle(task.title); // Reset to original if empty
      return;
    }

    setIsSaving(true);
    try {
      const result = await editTaskMutation.mutateAsync({
        taskId: task.id,
        userId: user.id,
        data: { title: editedTitle.trim() }
      });
      if (result) {
        onUpdated(result, false);
      }
    } catch (error) {
      console.error("Error saving title:", error);
      setEditedTitle(task.title); // Reset on error
    } finally {
      setIsSaving(false);
      setIsEditingTitle(false);
    }
  };

  // Save description changes
  const handleSaveDescription = async () => {
    if (!user || editedDescription === task.description) {
      setIsEditingDescription(false);
      return;
    }

    setIsSaving(true);
    try {
      const result = await editTaskMutation.mutateAsync({
        taskId: task.id,
        userId: user.id,
        data: { description: editedDescription.trim() || null }
      });
      if (result) {
        // Pass updated task data (no status change)
        onUpdated(result, false);
      }
    } catch (error) {
      console.error("Error saving description:", error);
      setEditedDescription(task.description || ""); // Reset on error
    } finally {
      setIsSaving(false);
      setIsEditingDescription(false);
    }
  };

  // Dismiss date editor without saving
  const handleDismissDateEditor = () => {
    setEditedDueDate(task.due_date ? new Date(task.due_date) : undefined);
    setIsEditingDueDate(false);
  };

  // Tag editing handlers
  const handleEditTag = async (tagId: string, newName: string): Promise<boolean> => {
    if (!user) return false;

    try {
      const updatedTag = await editTagMutation.mutateAsync({
        tagId,
        data: { name: newName }
      });
      if (updatedTag) {
        // TanStack Query automatically updates the cache
        // Create updated task object with new tag data
        const updatedTask = { ...task };
        // Pass updated task data (no status change) - tag name changes don't affect task object directly
        onUpdated(updatedTask, false);
        return true;
      }
      return false;
    } catch (error) {
      console.error("Error editing tag:", error);
      return false;
    }
  };

  const handleRemoveTagFromTask = async (tagId: string) => {
    if (!user) return;

    try {
      await removeTaskTagMutation.mutateAsync(tagId);
      // TanStack Query handles optimistic updates automatically
      // Create updated task object
      const updatedTask = { ...task };
      // Pass updated task data (no status change)
      onUpdated(updatedTask, false);
    } catch (error) {
      console.error("Error removing tag from task:", error);
    }
  };

  const handleDeleteTagGlobally = async (tagId: string): Promise<boolean> => {
    const tag = taskTags.find(t => t.id === tagId);
    if (!tag) return false;

    setTagToDelete(tag);
    setIsDeleteTagDialogOpen(true);
    return true;
  };

  const confirmDeleteTag = async () => {
    if (!user || !tagToDelete) return;

    try {
      await deleteTagMutation.mutateAsync(tagToDelete.id);
      // TanStack Query handles optimistic updates automatically
      // Create updated task object
      const updatedTask = { ...task };
      // Pass updated task data (no status change)
      onUpdated(updatedTask, false);
    } catch (error) {
      console.error("Error deleting tag globally:", error);
    } finally {
      setIsDeleteTagDialogOpen(false);
      setTagToDelete(null);
    }
  };

  // Inline tag picker handlers
  const handleAddTagToTask = async (tag: Tag) => {
    if (!user) return;

    // Check if tag already exists on task
    const tagExists = taskTags.some(t => t.id === tag.id);
    if (tagExists) return; // Don't add duplicate tags

    try {
      await addTaskTagMutation.mutateAsync(tag.id);
      // TanStack Query handles optimistic updates automatically
      // Create updated task object
      const updatedTask = { ...task };
      // Pass updated task data (no status change)
      onUpdated(updatedTask, false);
    } catch (error) {
      console.error("Error adding tag to task:", error);
    }
  };

  const handleCreateAndAddTag = async (name: string, color: string): Promise<Tag | null> => {
    if (!user) return null;

    try {
      const newTag = await createTagMutation.mutateAsync({ name, color });
      if (newTag) {
        // TanStack Query automatically updates the available tags cache
        // Add to task
        await handleAddTagToTask(newTag);
      }
      return newTag;
    } catch (error) {
      console.error("Error creating and adding tag:", error);
      return null;
    }
  };

  // Note: Filtering of available tags is now handled by the InlineTagPicker component
  // using the useSmartTagSearch hook, which automatically filters out selected tags

  const handleSearchAvailableTags = useCallback(async (searchTerm: string): Promise<Tag[]> => {
    if (!user) return [];

    try {
      const searchResults = await searchTags(user.id, searchTerm);
      // Note: Filtering out selected tags is now handled by the useSmartTagSearch hook
      // in the InlineTagPicker component, so we return all search results here
      return searchResults;
    } catch (error) {
      console.error("Error searching tags:", error);
      return [];
    }
  }, [user]);



  return (
    <>
      <GlassCard
        ref={(node) => {
          setNodeRef(node);
          cardRef.current = node;
        }}
        style={getCardStyles()}
        className={`${getBorderClass()} ${isCompleted && !isAnyTaskDragging ? 'opacity-75' : ''} group ${isDraggable ? 'cursor-grab hover:cursor-grabbing active:cursor-grabbing' : ''} ${isSubtask && !isInModal ? '!py-1' : ''}`}
        intensity="subtle"
        enableGlass={true}
        data-dragging={dndIsDragging}
        onTouchStart={handleMobileTouchStart}
        onTouchEnd={handleMobileTouchEnd}
        onPointerDown={handleMobilePointerDown}
        onPointerUp={handleMobilePointerUp}
        onClick={handleCardClick}
        {...(isDraggable ? attributes : {})}
        {...conditionalListeners}
      >
        <GlassCardContent
          className={`relative ${
            isSubtask && !isInModal
              ? 'py-1 pl-4 md:pl-3 pr-4 md:pr-3' // Reduced padding for subtasks since they have left margin
              : 'py-2 pl-5 md:pl-4 pr-12 md:pr-14'   // Normal padding for parent tasks and modal view
          }`}
          data-subtask={isSubtask ? "true" : undefined} // Mark subtasks for event handling
        >
          {/* Action Icons - Vertically Centered Right Side - Only show when inline editing is enabled and NOT a subtask in list view */}
          {isInlineEditEnabled && !(isSubtask && !isInModal) && (
            <div className={`absolute top-1/2 right-2 md:right-3 transform -translate-y-1/2 flex flex-col gap-1 ${
              isMobile
                ? (taskMode === "completion"
                    ? (shouldShowActionIcons || forceSelected ? 'opacity-100 pointer-events-auto' : 'opacity-0 pointer-events-none')
                    : (task.id === lastSelectedTaskId || forceSelected ? 'opacity-100 pointer-events-auto' : 'opacity-0 pointer-events-none'))
                : (taskMode === "completion"
                    ? (shouldShowActionIcons || forceSelected ? 'opacity-100 pointer-events-auto' : 'opacity-0 pointer-events-none group-hover:opacity-100 group-hover:pointer-events-auto')
                    : (taskMode === "selection" && (isGloballySelected || forceSelected) ? 'opacity-100 pointer-events-auto' : 'opacity-0 pointer-events-none group-hover:opacity-100 group-hover:pointer-events-auto'))
            } transition-all duration-200 z-10`}>
            <Button
              variant="ghost"
              size="icon"
              className="h-8 w-8 text-muted-foreground hover:text-foreground transition-colors"
              onClick={(e) => {
                e.stopPropagation();
                setIsEditDialogOpen(true);
              }}
              aria-label="Edit task"
            >
              <Pencil className="h-4 w-4" />
            </Button>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="ghost"
                  size="icon"
                  className={`h-8 w-8 transition-colors ${
                    task.parent_task_id
                      ? 'text-muted-foreground/50 cursor-not-allowed'
                      : 'text-muted-foreground hover:text-foreground'
                  }`}
                  onClick={(e) => {
                    e.stopPropagation();
                  }}
                  aria-label="Move to list"
                  disabled={!!task.parent_task_id}
                >
                  <ArrowLeftRight className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent
                align="end"
                onClick={(e) => e.stopPropagation()}
                className="max-h-[200px] overflow-y-auto"
              >
                {task.parent_task_id ? (
                  <DropdownMenuItem disabled className="text-muted-foreground text-xs">
                    Subtasks cannot be moved to other lists
                  </DropdownMenuItem>
                ) : (
                  availableLists
                    .map((list) => {
                    const hasColor = list.color !== null;
                    const taskCount = taskCounts?.[list.id] || 0;

                    // Get hover styles for list color with !important to override defaults
                    const getHoverStyles = (): React.CSSProperties => {
                      if (!hasColor) {
                        return {
                          '--hover-bg': '#f8f9fa',
                          '--hover-color': '#4b5563', // Brighter text color
                        } as React.CSSProperties;
                      }

                      const textColor = getContrastTextColor(list.color);
                      return {
                        '--hover-bg': list.color!,
                        '--hover-color': textColor,
                      } as React.CSSProperties;
                    };

                    const getBadgeStyles = () => {
                      if (!hasColor || !list.color) {
                        return {
                          backgroundColor: "#e9ecef",
                          color: "#4b5563", // Brighter text color
                          opacity: 0.8,
                        };
                      }

                      const textColor = getContrastTextColor(list.color);
                      return {
                        backgroundColor: list.color,
                        color: textColor,
                        opacity: 0.8,
                      };
                    };

                    return (
                      <DropdownMenuItem
                        key={list.id}
                        onClick={(e) => {
                          e.stopPropagation();
                          handleMoveToList(list.id);
                        }}
                        className="flex items-center gap-2 px-3 py-2 cursor-pointer transition-colors [&:hover]:bg-[var(--hover-bg)] [&:hover]:text-[var(--hover-color)]"
                        style={getHoverStyles() as React.CSSProperties}
                      >
                        {list.id === listId && (
                          <Check className="h-4 w-4 text-current" />
                        )}
                        <span className="truncate max-w-[120px] font-medium text-sm">
                          {list.name}
                        </span>
                        {taskCount > 0 && (
                          <Badge
                            className="text-xs border-transparent"
                            style={getBadgeStyles()}
                          >
                            {taskCount}
                          </Badge>
                        )}
                      </DropdownMenuItem>
                    );
                  })
                )}
                {!task.parent_task_id && availableLists.length === 0 && (
                  <DropdownMenuItem disabled>
                    No lists available
                  </DropdownMenuItem>
                )}
              </DropdownMenuContent>
            </DropdownMenu>
            <Button
              variant="ghost"
              size="icon"
              className="h-8 w-8 text-muted-foreground hover:text-foreground transition-colors"
              onClick={(e) => {
                e.stopPropagation();
                handleDuplicate();
              }}
              aria-label="Duplicate task"
            >
              <Copy className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="icon"
              className="h-8 w-8 text-muted-foreground hover:text-destructive transition-colors"
              onClick={(e) => {
                e.stopPropagation();
                setIsDeleteDialogOpen(true);
              }}
              aria-label="Delete task"
            >
              <Trash className="h-4 w-4" />
            </Button>
          </div>
          )}

          <div className="flex gap-3">
            {/* Left column: Checkbox */}
            <div className="flex flex-col items-center flex-shrink-0 justify-center relative min-h-full">

              {taskMode === "completion" ? (
                <Checkbox
                  checked={isCompleted}
                  onCheckedChange={(checked) => {
                    // Prevent parent interactions for subtasks
                    if (isSubtask) {
                      // Stop event propagation by handling it here
                      handleCheckboxClick();
                    } else {
                      handleCheckboxClick();
                    }
                  }}
                  className={`${isSubtask && !isInModal ? 'h-5 w-5' : 'h-6 w-6'} transition-all duration-300 glass-checkbox`}
                  style={{
                    backgroundColor: 'transparent',
                  } as React.CSSProperties}
                  data-testid="checkbox"
                />
              ) : (
                <button
                  type="button"
                  onClick={(e) => {
                    // Prevent parent interactions for subtasks
                    if (isSubtask) {
                      e.stopPropagation();
                    }
                    handleCheckboxClick();
                  }}
                  className={`${isSubtask && !isInModal ? 'h-5 w-5' : 'h-6 w-6'} rounded-full transition-all duration-200 flex items-center justify-center focus:outline-none glass-radio-selector`}
                  style={{
                    backgroundColor: 'transparent'
                  }}
                  data-testid="radio-selector"
                  aria-label={isGloballySelected ? "Deselect task" : "Select task"}
                >
                  {isGloballySelected && (
                    <div className={`${isSubtask && !isInModal ? 'w-2 h-2' : 'w-2.5 h-2.5'} rounded-full glass-radio-dot`} />
                  )}
                </button>
              )}
            </div>

            <div className="flex-1 min-w-0 max-w-full">
              {/* For subtasks in list view, show title, due dates, and tags - no descriptions or inline editing */}
              {isSubtask && !isInModal ? (
                <>
                  <div
                    className={`font-normal break-words overflow-hidden text-sm ${
                      isCompleted ? 'line-through text-muted-foreground' : ''
                    } cursor-pointer`}
                    onClick={(e) => {
                      e.stopPropagation();
                      setIsEditDialogOpen(true); // Open edit modal instead of inline editing
                    }}
                  >
                    {task.title}
                  </div>

                  {/* Due date display for subtasks */}
                  {task.due_date && (
                    <div className="flex items-center mt-1">
                      <Calendar className="h-3 w-3 mr-1 text-muted-foreground" />
                      <span className="text-xs text-muted-foreground">
                        {formatDateTime(task.due_date, hasTimeComponent(task.due_date))}
                      </span>
                    </div>
                  )}

                  {/* Tags display for subtasks */}
                  {taskTags.length > 0 && (
                    <div className="flex flex-wrap gap-1 mt-1">
                      {taskTags.map((tag) => (
                        <TagPill
                          key={tag.id}
                          tag={tag}
                          size="sm"
                          showRemove={false}
                          allowInlineEdit={false}
                          isCompleted={isCompleted}
                        />
                      ))}
                    </div>
                  )}
                </>
              ) : (
                <>
                  {/* Full task display for parent tasks and modal view */}
                  {isEditingTitle ? (
                    <div className="mb-1">
                      <Input
                        ref={titleInputRef}
                        value={editedTitle}
                        onChange={(e) => setEditedTitle(e.target.value)}
                        onBlur={handleSaveTitle}
                        onKeyDown={(e) => {
                          if (e.key === 'Enter') {
                            handleSaveTitle();
                          } else if (e.key === 'Escape') {
                            setEditedTitle(task.title);
                            setIsEditingTitle(false);
                          }
                        }}
                        className="py-0 h-7"
                        disabled={isSaving}
                      />
                    </div>
                  ) : (
                    <div
                      className={`font-normal break-words overflow-hidden ${
                        isCompleted ? 'line-through text-muted-foreground' : ''
                      } ${isInlineEditEnabled ? 'cursor-pointer' : 'cursor-default'}`}
                      onClick={(e) => {
                        if (isSubtask) e.stopPropagation();
                        if (!dndIsDragging && isInlineEditEnabled) setIsEditingTitle(true);
                      }}
                    >
                      {task.title}
                    </div>
                  )}

                  {/* Description, due date, and tags - only for parent tasks and modal view */}
                  {isEditingDescription ? (
                <div className="mb-1 mt-1">
                  <Textarea
                    ref={descriptionInputRef}
                    value={editedDescription}
                    onChange={(e) => setEditedDescription(e.target.value)}
                    onBlur={handleSaveDescription}
                    onKeyDown={(e) => {
                      if (e.key === 'Escape') {
                        setEditedDescription(task.description || "");
                        setIsEditingDescription(false);
                      }
                    }}
                    className="min-h-[60px] py-1 text-xs"
                    placeholder="Add a description..."
                    disabled={isSaving}
                  />
                </div>
              ) : (
                task.description ? (
                  <div
                    className={`break-words overflow-hidden ${
                      isSubtask && !isInModal ? 'text-xs mt-0.5' : 'text-xs mt-0.5' // Keep same size for now, but reduce margin for subtasks
                    } ${isCompleted ? 'text-muted-foreground/70' : 'text-muted-foreground'} ${isInlineEditEnabled ? 'cursor-pointer' : 'cursor-default'}`}
                    onClick={(e) => {
                      if (isSubtask) e.stopPropagation();
                      if (!dndIsDragging && isInlineEditEnabled) setIsEditingDescription(true);
                    }}
                  >
                    {(() => {
                      // Check if content is HTML (contains HTML tags) or plain text/markdown
                      const isHtml = /<[^>]*>/.test(task.description);

                      if (isHtml) {
                        // Render HTML content from rich text editor
                        return (
                          <div
                            className="text-xs"
                            dangerouslySetInnerHTML={{
                              __html: task.description
                                .replace(/<p><\/p>/g, '') // Remove empty paragraphs
                                .replace(/<p>/g, '<div class="mb-1">') // Convert p to div
                                .replace(/<\/p>/g, '</div>') // Convert p to div
                                .replace(/<ul[^>]*>/g, '<ul class="list-disc list-inside pl-2 space-y-0">') // Add list styling
                                .replace(/<ol[^>]*>/g, '<ol class="list-decimal list-inside pl-2 space-y-0">') // Add list styling
                                .replace(/<li>/g, '<li class="text-xs">') // Add list item styling
                                .replace(/<strong>/g, '<strong class="font-semibold">') // Add strong styling
                                .replace(/<em>/g, '<em class="italic">') // Add em styling
                                .replace(/<br\s*\/?>/g, '<br>') // Normalize br tags
                            }}
                          />
                        );
                      } else {
                        // Check if it's markdown (contains markdown syntax)
                        const isMarkdown = /(\*\*.*?\*\*|\*.*?\*|^- |^\d+\. )/m.test(task.description);

                        if (isMarkdown) {
                          // Simple markdown to HTML conversion for display
                          const htmlContent = task.description
                            .replace(/\*\*(.*?)\*\*/g, '<strong class="font-semibold">$1</strong>')
                            .replace(/\*(.*?)\*/g, '<em class="italic">$1</em>')
                            .replace(/^- (.+)$/gm, '<li class="text-xs">$1</li>')
                            .replace(/^\d+\. (.+)$/gm, '<li class="text-xs">$1</li>')
                            .replace(/\n/g, '<br>');

                          // Wrap list items in ul tags
                          const withLists = htmlContent
                            .replace(/(<li[^>]*>.*?<\/li>(?:\s*<br>\s*<li[^>]*>.*?<\/li>)*)/g, '<ul class="list-disc list-inside pl-2 space-y-0">$1</ul>')
                            .replace(/<br>\s*<ul/g, '<ul')
                            .replace(/<\/ul>\s*<br>/g, '</ul>');

                          return (
                            <div
                              className="text-xs whitespace-pre-wrap"
                              dangerouslySetInnerHTML={{ __html: withLists }}
                            />
                          );
                        } else {
                          // Render plain text with line breaks preserved
                          return (
                            <span className="whitespace-pre-wrap text-xs">
                              {task.description}
                            </span>
                          );
                        }
                      }
                    })()}
                  </div>
                ) : isInlineEditEnabled ? (
                  <p
                    className={`text-xs mt-0.5 break-words overflow-hidden text-muted-foreground/50 cursor-pointer`}
                    onClick={(e) => {
                      if (isSubtask) e.stopPropagation();
                      if (!dndIsDragging) setIsEditingDescription(true);
                    }}
                  >
                    Add a description...
                  </p>
                ) : null
              )}

              {isEditingDueDate ? (
                <div
                  ref={datePickerRef}
                  className="mt-1"
                  onKeyDown={(e) => {
                    if (e.key === 'Escape') {
                      handleDismissDateEditor();
                    }
                  }}
                >
                  <DateTimePicker
                    date={editedDueDate}
                    setDate={(date) => {
                      // Update state
                      setEditedDueDate(date);

                      // If a date is selected or cleared, save immediately
                      if (user) {
                        setIsSaving(true);
                        editTaskMutation.mutateAsync({
                          taskId: task.id,
                          userId: user.id,
                          data: { due_date: date === null ? null : date }
                        })
                          .then((result) => {
                            if (result) {
                              // Pass updated task data (no status change)
                              onUpdated(result, false);
                            }
                            // Close the editor after successful save
                            setIsEditingDueDate(false);
                          })
                          .catch((error) => {
                            console.error("Error saving date:", error);
                            // Revert to original date on error
                            setEditedDueDate(task.due_date ? new Date(task.due_date) : undefined);
                          })
                          .finally(() => {
                            setIsSaving(false);
                          });
                      }
                    }}
                    placeholder="Select due date"
                    disabled={isSaving}
                    className="w-full"
                    includeTime={true}
                  />
                </div>
              ) : (
                task.due_date ? (
                  <div
                    className={`flex items-center mt-1 ${isInlineEditEnabled ? 'cursor-pointer' : 'cursor-default'}`}
                    onClick={(e) => {
                      if (isSubtask) e.stopPropagation();
                      if (!dndIsDragging && isInlineEditEnabled) setIsEditingDueDate(true);
                    }}
                  >
                    <Calendar className="h-3.5 w-3.5 mr-1 text-muted-foreground" />
                    <span className="text-xs text-muted-foreground">
                      {formatDateTime(task.due_date, hasTimeComponent(task.due_date))}
                    </span>
                  </div>
                ) : isInlineEditEnabled ? (
                  <div
                    className={`flex items-center mt-1 cursor-pointer`}
                    onClick={(e) => {
                      if (isSubtask) e.stopPropagation();
                      if (!dndIsDragging) setIsEditingDueDate(true);
                    }}
                  >
                    <Calendar className="h-3.5 w-3.5 mr-1 text-muted-foreground" />
                    <span className="text-xs text-muted-foreground">
                      Add due date...
                    </span>
                  </div>
                ) : null
              )}

              {/* Tags Display */}
              {(taskTags.length > 0 || isInlineEditEnabled) && (
                <div className="flex flex-wrap gap-1 mt-2">
                  {taskTags.map((tag) => (
                    <TagPill
                      key={tag.id}
                      tag={tag}
                      size="sm"
                      showRemove={isInlineEditEnabled}
                      allowInlineEdit={isInlineEditEnabled}
                      isCompleted={isCompleted}
                      onRemove={isInlineEditEnabled ? () => handleRemoveTagFromTask(tag.id) : undefined}
                      onEdit={isInlineEditEnabled ? handleEditTag : undefined}
                      onDelete={isInlineEditEnabled ? handleDeleteTagGlobally : undefined}
                    />
                  ))}

                  {/* Inline Tag Picker - Only show when inline editing is enabled */}
                  {isInlineEditEnabled && (
                    <InlineTagPicker
                      selectedTags={taskTags}
                      availableTags={availableTags}
                      onTagSelect={handleAddTagToTask}
                      onTagRemove={(tag) => handleRemoveTagFromTask(tag.id)}
                      onTagCreate={handleCreateAndAddTag}
                      onSearchTags={handleSearchAvailableTags}
                      size="sm"
                      disabled={dndIsDragging}
                    />
                  )}
                </div>
              )}
                </>
              )}
            </div>
          </div>
        </GlassCardContent>

        {/* Subtasks Container - Separate from parent task content */}
        {/* Render incomplete subtasks for parent tasks only (not for subtasks themselves) */}
        {!task.parent_task_id && !isSubtask && subtaskItems.length > 0 && sortOption === "position" && (
          <div
            className="px-3 pb-2"
            onPointerDown={(e) => {
              // Prevent parent drag when interacting with subtasks
              e.stopPropagation();
            }}
            onMouseDown={(e) => {
              // Prevent parent drag when interacting with subtasks
              e.stopPropagation();
            }}
          >
              <DndContext
                id={`subtasks-${task.id}`}
                sensors={subtaskSensors}
                collisionDetection={closestCenter}
                onDragStart={handleSubtaskDragStart}
                onDragEnd={handleSubtaskDragEnd}
                modifiers={[restrictToVerticalAxis]}
              >
                <SortableContext
                  items={subtaskItems.map(subtask => subtask.id)}
                  strategy={verticalListSortingStrategy}
                >
                  <div className="space-y-0.5 flex flex-col w-full">
                    {subtaskItems.map((subtask) => (
                      <TaskItem
                        key={subtask.id}
                        task={subtask}
                        onUpdated={onUpdated}
                        onDeleted={onDeleted}
                        isDraggable={true}
                        isAnyTaskDragging={isSubtaskDragging}
                        listId={listId}
                        sortOption={sortOption}
                        listColor={listColor}
                        taskCounts={taskCounts}
                        forceSelected={forceSelected}
                        taskMode={taskMode}
                        selectedTaskIds={selectedTaskIds}
                        onTaskSelectionChange={onTaskSelectionChange}
                        lastSelectedTaskId={lastSelectedTaskId}
                        isInlineEditEnabled={isInlineEditEnabled}
                        activeActionIconsTaskId={activeActionIconsTaskId}
                        onActionIconsChange={onActionIconsChange}
                        allTasks={allTasks}
                        onNavigateToTask={onNavigateToTask}
                        isTagFiltered={isTagFiltered}
                        isSubtask={true}
                        parentTask={task}
                      />
                    ))}
                  </div>
                </SortableContext>

                {/* Drag Overlay for subtasks */}
                <DragOverlay
                  adjustScale={false}
                  dropAnimation={null}
                  style={{
                    transformOrigin: '0 0',
                    transition: 'none',
                  }}
                >
                  {activeSubtask ? (
                    <div className="drag-overlay">
                      <TaskItem
                        task={activeSubtask}
                        onUpdated={() => {}}
                        onDeleted={() => {}}
                        isDraggable={false}
                        isAnyTaskDragging={true}
                        listId={listId}
                        sortOption={sortOption}
                        listColor={listColor}
                        taskCounts={taskCounts}
                        taskMode={taskMode}
                        selectedTaskIds={selectedTaskIds}
                        onTaskSelectionChange={onTaskSelectionChange}
                        lastSelectedTaskId={lastSelectedTaskId}
                        forceSelected={true}
                        isInlineEditEnabled={isInlineEditEnabled}
                        activeActionIconsTaskId={activeActionIconsTaskId}
                        onActionIconsChange={onActionIconsChange}
                        allTasks={allTasks}
                        onNavigateToTask={onNavigateToTask}
                        isTagFiltered={isTagFiltered}
                        isSubtask={true}
                        parentTask={task}
                      />
                    </div>
                  ) : null}
                </DragOverlay>
              </DndContext>
            </div>
          )}

        {/* Completed Subtasks Section - Draggable case */}
        {!task.parent_task_id && !isSubtask && completedSubtasks.length > 0 && sortOption === "position" && (
          <div className="px-3 pb-2">
            <Collapsible
              open={isCompletedSubtasksOpen}
              onOpenChange={setIsCompletedSubtasksOpen}
            >
              <CollapsibleTrigger asChild>
                <Button
                  variant="ghost"
                  className="flex items-center gap-2 w-full justify-start p-0 h-auto text-muted-foreground hover:text-foreground"
                >
                  {isCompletedSubtasksOpen ? (
                    <ChevronDown className="h-3 w-3" />
                  ) : (
                    <ChevronRight className="h-3 w-3" />
                  )}
                  <span className="text-xs font-medium">
                    Completed Subtasks ({completedSubtasks.length})
                  </span>
                </Button>
              </CollapsibleTrigger>
              <CollapsibleContent className="mt-2">
                <div className="space-y-0.5 flex flex-col w-full">
                  {completedSubtasks.map((subtask) => (
                    <TaskItem
                      key={subtask.id}
                      task={subtask}
                      onUpdated={onUpdated}
                      onDeleted={onDeleted}
                      isDraggable={false}
                      isAnyTaskDragging={isAnyTaskDragging}
                      listId={listId}
                      sortOption={sortOption}
                      listColor={listColor}
                      taskCounts={taskCounts}
                      forceSelected={forceSelected}
                      taskMode={taskMode}
                      selectedTaskIds={selectedTaskIds}
                      onTaskSelectionChange={onTaskSelectionChange}
                      lastSelectedTaskId={lastSelectedTaskId}
                      isInlineEditEnabled={isInlineEditEnabled}
                      activeActionIconsTaskId={activeActionIconsTaskId}
                      onActionIconsChange={onActionIconsChange}
                      allTasks={allTasks}
                      onNavigateToTask={onNavigateToTask}
                      isTagFiltered={isTagFiltered}
                      isSubtask={true}
                      parentTask={task}
                    />
                  ))}
                </div>
              </CollapsibleContent>
            </Collapsible>
          </div>
        )}

        {/* Render incomplete subtasks without drag and drop when not sorted by position */}
        {!task.parent_task_id && !isSubtask && incompleteSubtasks.length > 0 && sortOption !== "position" && (
          <div className="pl-4 pr-3 pb-2">
            <div className="space-y-0.5 flex flex-col w-full">
              {incompleteSubtasks.map((subtask) => (
                <TaskItem
                  key={subtask.id}
                  task={subtask}
                  onUpdated={onUpdated}
                  onDeleted={onDeleted}
                  isDraggable={false}
                  isAnyTaskDragging={isAnyTaskDragging}
                  listId={listId}
                  sortOption={sortOption}
                  listColor={listColor}
                  taskCounts={taskCounts}
                  forceSelected={forceSelected}
                  taskMode={taskMode}
                  selectedTaskIds={selectedTaskIds}
                  onTaskSelectionChange={onTaskSelectionChange}
                  lastSelectedTaskId={lastSelectedTaskId}
                  isInlineEditEnabled={isInlineEditEnabled}
                  activeActionIconsTaskId={activeActionIconsTaskId}
                  onActionIconsChange={onActionIconsChange}
                  allTasks={allTasks}
                  onNavigateToTask={onNavigateToTask}
                  isTagFiltered={isTagFiltered}
                  isSubtask={true}
                  parentTask={task}
                />
              ))}
            </div>
          </div>
        )}

        {/* Completed Subtasks Section - Non-draggable case */}
        {!task.parent_task_id && !isSubtask && completedSubtasks.length > 0 && sortOption !== "position" && (
          <div className="pl-4 pr-3 pb-2">
            <Collapsible
              open={isCompletedSubtasksOpen}
              onOpenChange={setIsCompletedSubtasksOpen}
            >
              <CollapsibleTrigger asChild>
                <Button
                  variant="ghost"
                  className="flex items-center gap-2 w-full justify-start p-0 h-auto text-muted-foreground hover:text-foreground"
                >
                  {isCompletedSubtasksOpen ? (
                    <ChevronDown className="h-3 w-3" />
                  ) : (
                    <ChevronRight className="h-3 w-3" />
                  )}
                  <span className="text-xs font-medium">
                    Completed Subtasks ({completedSubtasks.length})
                  </span>
                </Button>
              </CollapsibleTrigger>
              <CollapsibleContent className="mt-2">
                <div className="space-y-0.5 flex flex-col w-full">
                  {completedSubtasks.map((subtask) => (
                    <TaskItem
                      key={subtask.id}
                      task={subtask}
                      onUpdated={onUpdated}
                      onDeleted={onDeleted}
                      isDraggable={false}
                      isAnyTaskDragging={isAnyTaskDragging}
                      listId={listId}
                      sortOption={sortOption}
                      listColor={listColor}
                      taskCounts={taskCounts}
                      forceSelected={forceSelected}
                      taskMode={taskMode}
                      selectedTaskIds={selectedTaskIds}
                      onTaskSelectionChange={onTaskSelectionChange}
                      lastSelectedTaskId={lastSelectedTaskId}
                      isInlineEditEnabled={isInlineEditEnabled}
                      activeActionIconsTaskId={activeActionIconsTaskId}
                      onActionIconsChange={onActionIconsChange}
                      allTasks={allTasks}
                      onNavigateToTask={onNavigateToTask}
                      isTagFiltered={isTagFiltered}
                      isSubtask={true}
                      parentTask={task}
                    />
                  ))}
                </div>
              </CollapsibleContent>
            </Collapsible>
          </div>
        )}
      </GlassCard>

      <EditTaskDialog
        task={currentEditingTask}
        listId={currentEditingTask.list_id}
        open={isEditDialogOpen}
        onOpenChange={setIsEditDialogOpen}
        onTaskUpdated={handleTaskUpdatedFromDialog}
        allTasks={allTasks}
        currentTaskIndex={currentEditingIndex}
        onNavigateToTask={handleNavigateToTask}
      />

      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent aria-labelledby="delete-task-title">
          <AlertDialogTitle id="delete-task-title" className="sr-only">
            Delete Task
          </AlertDialogTitle>
          <AlertDialogDescription>
            Are you sure you want to delete this task? This action cannot be undone.
          </AlertDialogDescription>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={handleDelete} className="bg-destructive text-destructive-foreground">
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Delete Tag Confirmation Dialog */}
      <AlertDialog open={isDeleteTagDialogOpen} onOpenChange={setIsDeleteTagDialogOpen}>
        <AlertDialogContent aria-labelledby="delete-tag-title">
          <AlertDialogTitle id="delete-tag-title" className="sr-only">
            Delete Tag
          </AlertDialogTitle>
          <AlertDialogDescription>
            Are you sure you want to delete the tag "{tagToDelete?.name}"? This will remove it from all tasks that use this tag. This action cannot be undone.
          </AlertDialogDescription>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={confirmDeleteTag}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              Delete Tag
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}
