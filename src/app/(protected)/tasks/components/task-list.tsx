"use client";

import { useState, useRef } from "react";
import { useUser } from "@stackframe/stack";
import { Task, TaskSortOption } from "@/lib/db";
import { useReorderTasksMutation, usePromoteSubtaskMutation, useNestTaskMutation } from "@/lib/queries";
import { TaskItem } from "./task-item";
import { QuickAddTask } from "./quick-add-task";
import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  TouchSensor,
  MouseSensor,
  useSensor,
  useSensors,
  DragEndEvent,
  DragStartEvent,
  DragOverEvent,
  DragOverlay,
  MeasuringStrategy,
} from "@dnd-kit/core";
import {
  restrictToVerticalAxis,
} from "@dnd-kit/modifiers";
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  verticalListSortingStrategy,
} from "@dnd-kit/sortable";

interface TaskListProps {
  tasks: Task[]; // Parent tasks only (filtered)
  allTasks?: Task[]; // All tasks including subtasks (for TaskItem to find its subtasks)
  onTaskUpdated: (updatedTask?: Task, statusChanged?: boolean) => void;
  onTaskDeleted: (deletedTaskId?: string) => void;
  onTasksReordered: (tasks: Task[]) => void;
  sortOption: TaskSortOption;
  listId: string;
  listColor?: string | null;
  taskCounts?: Record<string, number>;
  taskMode?: "completion" | "selection";
  selectedTaskIds?: Set<string>;
  onTaskSelectionChange?: (selectedIds: Set<string>) => void;
  lastSelectedTaskId?: string | null;
  showQuickAdd?: boolean;
  onAddTaskClick?: () => void;
  isInlineEditEnabled?: boolean;
  activeActionIconsTaskId?: string | null;
  onActionIconsChange?: (taskId: string | null) => void;
  onNavigateToTask?: (task: Task) => void; // Callback to switch to a different task
  isTagFiltered?: boolean; // Whether we're in tag-filtered view
}

export function TaskList({
  tasks,
  allTasks,
  onTaskUpdated,
  onTaskDeleted,
  onTasksReordered,
  sortOption,
  listId,
  listColor,
  taskCounts,
  taskMode = "completion",
  selectedTaskIds = new Set(),
  onTaskSelectionChange,
  lastSelectedTaskId = null,
  showQuickAdd = false,
  onAddTaskClick,
  isInlineEditEnabled = true,
  activeActionIconsTaskId = null,
  onActionIconsChange,
  onNavigateToTask,
  isTagFiltered = false,
}: TaskListProps) {
  const user = useUser();
  const [items, setItems] = useState(tasks);
  const [isDragging, setIsDragging] = useState(false);
  const [activeTask, setActiveTask] = useState<Task | null>(null);
  const lastDragOverRef = useRef<string | null>(null);
  const isReorderingRef = useRef(false);

  // TanStack Query mutations
  const reorderTasksMutation = useReorderTasksMutation(listId, sortOption);
  const promoteSubtaskMutation = usePromoteSubtaskMutation(listId, sortOption);
  const nestTaskMutation = useNestTaskMutation(listId, sortOption);

  // Update local state when tasks prop changes, but not during or immediately after reordering
  if (!isReorderingRef.current && JSON.stringify(tasks) !== JSON.stringify(items)) {
    setItems(tasks);
  }

  // Optimized sensors for both desktop and mobile
  const sensors = useSensors(
    // Mouse sensor for desktop
    useSensor(MouseSensor, {
      activationConstraint: {
        distance: 8, // Require 8px movement to start drag (allows for scrolling)
      },
    }),
    // Touch sensor for mobile with press-and-hold
    useSensor(TouchSensor, {
      activationConstraint: {
        delay: 250, // 250ms press-and-hold to start drag
        tolerance: 5, // Allow 5px of movement during the delay
      },
    }),
    // Keyboard sensor for accessibility
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  // Disable all animations for instant positioning
  const measuring = {
    droppable: {
      strategy: MeasuringStrategy.Always,
    },
  };

  // Layout animations are disabled at the TaskItem level for instant positioning



  // Helper function to get all draggable items with hierarchical IDs
  const getAllDraggableItems = () => {
    const draggableItems: string[] = [];

    items.forEach(parentTask => {
      // Add parent task with 'task-' prefix
      draggableItems.push(`task-${parentTask.id}`);

      // Add subtasks with 'subtask-' prefix
      const subtasks = allTasks?.filter(t => t.parent_task_id === parentTask.id && t.status !== 'completed') || [];
      subtasks.forEach(subtask => {
        draggableItems.push(`subtask-${subtask.id}`);
      });
    });

    return draggableItems;
  };

  // Helper function to find task by hierarchical ID
  const findTaskByHierarchicalId = (hierarchicalId: string): Task | null => {
    if (hierarchicalId.startsWith('subtask-')) {
      const subtaskId = hierarchicalId.replace('subtask-', '');
      return allTasks?.find(t => t.id === subtaskId) || null;
    } else if (hierarchicalId.startsWith('task-')) {
      const taskId = hierarchicalId.replace('task-', '');
      return items.find(t => t.id === taskId) || null;
    } else {
      // Fallback for legacy IDs
      return items.find(t => t.id === hierarchicalId) || null;
    }
  };

  const handleDragStart = (event: DragStartEvent) => {
    setIsDragging(true);
    isReorderingRef.current = true; // Prevent prop updates during drag
    lastDragOverRef.current = null; // Reset drag over tracking

    const activeId = event.active.id as string;
    const draggedTask = findTaskByHierarchicalId(activeId);
    setActiveTask(draggedTask);

    // Add haptic feedback on mobile if available
    if ('vibrate' in navigator) {
      navigator.vibrate(50);
    }
  };

  const handleDragOver = (event: DragOverEvent) => {
    const { over } = event;

    // Only provide haptic feedback when moving to a new target
    if (over && over.id !== lastDragOverRef.current) {
      lastDragOverRef.current = over.id as string;

      // Add haptic feedback when dragging over a new task
      if ('vibrate' in navigator) {
        navigator.vibrate(30);
      }
    }
  };

  // Helper functions for different drag scenarios
  const handleSubtaskReorder = async (draggedSubtask: Task, targetSubtask: Task) => {
    if (!draggedSubtask.parent_task_id || !user) return;

    // Get all subtasks of the parent
    const parentSubtasks = allTasks?.filter(t =>
      t.parent_task_id === draggedSubtask.parent_task_id && t.status !== 'completed'
    ) || [];

    const oldIndex = parentSubtasks.findIndex(t => t.id === draggedSubtask.id);
    const newIndex = parentSubtasks.findIndex(t => t.id === targetSubtask.id);

    if (oldIndex === -1 || newIndex === -1) return;

    const reorderedSubtasks = arrayMove(parentSubtasks, oldIndex, newIndex);
    const taskIds = reorderedSubtasks.map(t => t.id);

    // Use mutate for fire-and-forget with optimistic updates
    reorderTasksMutation.mutate({ userId: user.id, taskIds });
  };

  const handleSubtaskPromotion = async (draggedSubtask: Task, targetTask: Task) => {
    if (!user) return;

    // Calculate new position - place after target task
    const targetIndex = items.findIndex(t => t.id === targetTask.id);
    const newPosition = targetIndex + 1;

    // Use mutate for fire-and-forget with optimistic updates
    promoteSubtaskMutation.mutate({
      subtaskId: draggedSubtask.id,
      userId: user.id,
      newPosition
    });
  };

  const handleParentTaskReorder = async (draggedTask: Task, targetTask: Task) => {
    if (!user) return;

    const oldIndex = items.findIndex(t => t.id === draggedTask.id);
    const newIndex = items.findIndex(t => t.id === targetTask.id);

    if (oldIndex === -1 || newIndex === -1) return;

    const newItems = arrayMove(items, oldIndex, newIndex);
    setItems(newItems);
    onTasksReordered(newItems);

    const taskIds = newItems.map(t => t.id);
    // Use mutate for fire-and-forget with optimistic updates
    reorderTasksMutation.mutate({ userId: user.id, taskIds });
  };

  const handleTaskNesting = async (draggedTask: Task, parentTaskId: string) => {
    if (!user) return;

    // Use mutate for fire-and-forget with optimistic updates
    nestTaskMutation.mutate({
      taskId: draggedTask.id,
      parentTaskId,
      userId: user.id
    });
  };

  const handleDragEnd = async (event: DragEndEvent) => {
    const { active, over } = event;

    // Immediately clear drag state for instant response
    setIsDragging(false);
    setActiveTask(null);
    lastDragOverRef.current = null; // Reset drag over tracking

    if (!over || active.id === over.id || !user) {
      // Reset reordering flag immediately if no operation occurred
      isReorderingRef.current = false;
      return;
    }

    const activeId = active.id as string;
    const overId = over.id as string;

    const draggedTask = findTaskByHierarchicalId(activeId);
    const targetTask = findTaskByHierarchicalId(overId);

    if (!draggedTask || !targetTask) {
      isReorderingRef.current = false;
      return;
    }

    // Determine the type of drag operation
    const isDraggingSubtask = activeId.startsWith('subtask-');
    const isDraggingParentTask = activeId.startsWith('task-') || !draggedTask.parent_task_id;
    const isTargetSubtask = overId.startsWith('subtask-');
    const isTargetParentTask = overId.startsWith('task-') || !targetTask.parent_task_id;

    try {
      if (isDraggingSubtask && isTargetSubtask && draggedTask.parent_task_id === targetTask.parent_task_id) {
        // Scenario 1: Reordering subtasks within the same parent
        await handleSubtaskReorder(draggedTask, targetTask);
      } else if (isDraggingSubtask && isTargetParentTask) {
        // Scenario 2: Promoting subtask to standalone task
        await handleSubtaskPromotion(draggedTask, targetTask);
      } else if (isDraggingParentTask && isTargetParentTask) {
        // Scenario 3: Reordering parent tasks
        await handleParentTaskReorder(draggedTask, targetTask);
      } else if (isDraggingParentTask && isTargetSubtask) {
        // Scenario 4: Nesting standalone task as subtask
        if (targetTask.parent_task_id) {
          await handleTaskNesting(draggedTask, targetTask.parent_task_id);
        }
      }
    } catch (error) {
      console.error('Error handling drag operation:', error);
      // Revert optimistic updates on error
      setItems(tasks);
    } finally {
      // Allow prop updates again after a short delay to prevent visual jumps
      setTimeout(() => {
        isReorderingRef.current = false;
      }, 100);
    }
  };

  return (
    <div className={`space-y-2 ${isDragging ? 'dnd-context-dragging' : ''}`}>
      {sortOption === "position" ? (
        <DndContext
          id={`parent-tasks-${listId}`}
          sensors={sensors}
          collisionDetection={closestCenter}
          onDragStart={handleDragStart}
          onDragOver={handleDragOver}
          onDragEnd={handleDragEnd}
          modifiers={[restrictToVerticalAxis]}
          measuring={measuring}
        >
          <SortableContext
            items={getAllDraggableItems()}
            strategy={verticalListSortingStrategy}
          >
            {items.map((task) => (
              <TaskItem
                key={task.id}
                task={task}
                onUpdated={onTaskUpdated}
                onDeleted={onTaskDeleted}
                isDraggable={true}
                isAnyTaskDragging={isDragging}
                listId={listId}
                sortOption={sortOption}
                listColor={listColor}
                taskCounts={taskCounts}
                taskMode={taskMode}
                selectedTaskIds={selectedTaskIds}
                onTaskSelectionChange={onTaskSelectionChange}
                lastSelectedTaskId={lastSelectedTaskId}
                isInlineEditEnabled={isInlineEditEnabled}
                activeActionIconsTaskId={activeActionIconsTaskId}
                onActionIconsChange={onActionIconsChange}
                allTasks={allTasks || tasks}
                onNavigateToTask={onNavigateToTask}
                isTagFiltered={isTagFiltered}
              />
            ))}
          </SortableContext>

          {/* Drag Overlay to prevent card compression and maintain visual consistency */}
          <DragOverlay
            adjustScale={false}
            dropAnimation={null}
            style={{
              transformOrigin: '0 0',
              transition: 'none',
            }}
          >
            {activeTask ? (
              <div className="drag-overlay">
                <TaskItem
                  task={activeTask}
                  onUpdated={() => {}}
                  onDeleted={() => {}}
                  isDraggable={false}
                  isAnyTaskDragging={true}
                  listId={listId}
                  sortOption={sortOption}
                  listColor={listColor}
                  taskCounts={taskCounts}
                  taskMode={taskMode}
                  selectedTaskIds={selectedTaskIds}
                  onTaskSelectionChange={onTaskSelectionChange}
                  lastSelectedTaskId={lastSelectedTaskId}
                  forceSelected={true}
                  isInlineEditEnabled={isInlineEditEnabled}
                  activeActionIconsTaskId={activeActionIconsTaskId}
                  onActionIconsChange={onActionIconsChange}
                />
              </div>
            ) : null}
          </DragOverlay>
        </DndContext>
      ) : (
        // Non-draggable list for when sorted by title or due date
        items.map((task) => (
          <TaskItem
            key={task.id}
            task={task}
            onUpdated={onTaskUpdated}
            onDeleted={onTaskDeleted}
            isDraggable={false}
            isAnyTaskDragging={false}
            listId={listId}
            sortOption={sortOption}
            listColor={listColor}
            taskCounts={taskCounts}
            taskMode={taskMode}
            selectedTaskIds={selectedTaskIds}
            onTaskSelectionChange={onTaskSelectionChange}
            lastSelectedTaskId={lastSelectedTaskId}
            isInlineEditEnabled={isInlineEditEnabled}
            activeActionIconsTaskId={activeActionIconsTaskId}
            onActionIconsChange={onActionIconsChange}
            allTasks={allTasks || tasks}
            onNavigateToTask={onNavigateToTask}
            isTagFiltered={isTagFiltered}
          />
        ))
      )}

      {/* Quick Add Task Interface */}
      {showQuickAdd && onAddTaskClick && (
        <QuickAddTask
          onAddTaskClick={onAddTaskClick}
        />
      )}
    </div>
  );
}
