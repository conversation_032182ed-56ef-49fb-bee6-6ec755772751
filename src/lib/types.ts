import { TaskActivity, Task, List } from './db';

// Extended type to include task_title from the join query
export interface ActivityWithTaskTitle extends TaskActivity {
  task_title: string;
}

// Extended type to include list information from the join query
export interface TaskWithList extends Task {
  list_color: string | null;
}

// Extended type to include subtasks
export interface TaskWithSubtasks extends Task {
  subtasks?: Task[];
}

// Undo/Redo Types
export type UndoRedoActionType =
  | 'task_edit'
  | 'task_status_change'
  | 'task_create'
  | 'task_delete'
  | 'task_reorder'
  | 'task_move_list'
  | 'task_duplicate'
  | 'subtask_create'
  | 'subtask_delete'
  | 'list_reorder';

export interface BaseUndoRedoAction {
  id: string;
  type: UndoRedoActionType;
  timestamp: number;
  description: string;
}

export interface TaskEditAction extends BaseUndoRedoAction {
  type: 'task_edit';
  taskId: string;
  listId: string;
  previousData: Partial<Task>;
  newData: Partial<Task>;
}

export interface TaskStatusChangeAction extends BaseUndoRedoAction {
  type: 'task_status_change';
  taskId: string;
  listId: string;
  previousStatus: string;
  newStatus: string;
}

export interface TaskCreateAction extends BaseUndoRedoAction {
  type: 'task_create';
  task: Task;
  listId: string;
}

export interface TaskDeleteAction extends BaseUndoRedoAction {
  type: 'task_delete';
  task: Task;
  listId: string;
  position: number;
}

export interface TaskReorderAction extends BaseUndoRedoAction {
  type: 'task_reorder';
  listId: string;
  previousOrder: string[];
  newOrder: string[];
}

export interface TaskMoveListAction extends BaseUndoRedoAction {
  type: 'task_move_list';
  taskId: string;
  task: Task;
  sourceListId: string;
  targetListId: string;
  sourcePosition: number;
  targetPosition: number;
}

export interface TaskDuplicateAction extends BaseUndoRedoAction {
  type: 'task_duplicate';
  originalTaskId: string;
  duplicatedTask: Task;
  listId: string;
  position: number;
}

export interface SubtaskCreateAction extends BaseUndoRedoAction {
  type: 'subtask_create';
  subtask: Task;
  parentTaskId: string;
  listId: string;
}

export interface SubtaskDeleteAction extends BaseUndoRedoAction {
  type: 'subtask_delete';
  subtask: Task;
  parentTaskId: string;
  listId: string;
  position: number;
}

export interface ListReorderAction extends BaseUndoRedoAction {
  type: 'list_reorder';
  previousOrder: string[];
  newOrder: string[];
}

export type UndoRedoAction =
  | TaskEditAction
  | TaskStatusChangeAction
  | TaskCreateAction
  | TaskDeleteAction
  | TaskReorderAction
  | TaskMoveListAction
  | TaskDuplicateAction
  | SubtaskCreateAction
  | SubtaskDeleteAction
  | ListReorderAction;

export interface UndoRedoState {
  undoStack: UndoRedoAction[];
  redoStack: UndoRedoAction[];
  maxHistorySize: number;
}

export interface UndoRedoContextType {
  canUndo: boolean;
  canRedo: boolean;
  undo: () => Promise<void>;
  redo: () => Promise<void>;
  addAction: (action: UndoRedoAction) => void;
  clearHistory: () => void;
  getLastAction: () => UndoRedoAction | null;
  getLastRedoAction: () => UndoRedoAction | null;
}

// Tag Filter Types
export interface TagFilter {
  id: string;
  name: string;
  color: string;
}

export interface TagFilterState {
  activeTag: TagFilter | null;
  isActive: boolean;
}
